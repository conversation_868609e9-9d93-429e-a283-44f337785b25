import { useState, useEffect, useRef, useCallback } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, use<PERSON>ap<PERSON><PERSON>s, Popup } from 'react-leaflet';
import L from 'leaflet';
import 'leaflet/dist/leaflet.css';
import '../assets/css/location-picker.css';

// Add CSS animations for pulsing effect and search results
const addPulseStyles = () => {
  if (typeof document !== 'undefined') {
    const styleId = 'pulse-animations';
    if (!document.getElementById(styleId)) {
      const style = document.createElement('style');
      style.id = styleId;
      style.textContent = `
        @keyframes pulse-core {
          0% {
            transform: translate(-50%, -50%) scale(1);
            opacity: 1;
            box-shadow: 0 0 0 0 rgba(239, 68, 68, 0.7);
          }
          50% {
            transform: translate(-50%, -50%) scale(1.15);
            opacity: 0.9;
            box-shadow: 0 0 0 10px rgba(239, 68, 68, 0);
          }
          100% {
            transform: translate(-50%, -50%) scale(1);
            opacity: 1;
            box-shadow: 0 0 0 0 rgba(239, 68, 68, 0);
          }
        }
        
        @keyframes pulse-ring-1 {
          0% {
            transform: translate(-50%, -50%) scale(0.9);
            opacity: 0.6;
          }
          25% {
            transform: translate(-50%, -50%) scale(1.4);
            opacity: 0.2;
          }
          50% {
            transform: translate(-50%, -50%) scale(1.8);
            opacity: 0;
          }
          100% {
            transform: translate(-50%, -50%) scale(2.2);
            opacity: 0;
          }
        }
        
        @keyframes pulse-ring-2 {
          0% {
            transform: translate(-50%, -50%) scale(0.6);
            opacity: 0.6;
          }
          25% {
            transform: translate(-50%, -50%) scale(1.4);
            opacity: 0.2;
          }
          50% {
            transform: translate(-50%, -50%) scale(2.2);
            opacity: 0.1;
          }
          75% {
            transform: translate(-50%, -50%) scale(3);
            opacity: 0;
          }
          100% {
            transform: translate(-50%, -50%) scale(3.8);
            opacity: 0;
          }
        }
        
        @keyframes pulse-ring-3 {
          0% {
            transform: translate(-50%, -50%) scale(0.4);
            opacity: 0.4;
          }
          50% {
            transform: translate(-50%, -50%) scale(2.8);
            opacity: 0.1;
          }
          100% {
            transform: translate(-50%, -50%) scale(4.2);
            opacity: 0;
          }
        }
        
        @keyframes slideInUp {
          0% {
            opacity: 0;
            transform: translateY(20px) scale(0.95);
          }
          100% {
            opacity: 1;
            transform: translateY(0) scale(1);
          }
        }
        
        .pulse-core {
          animation: pulse-core 3s cubic-bezier(0.25, 0.46, 0.45, 0.94) infinite;
        }
        
        .pulse-ring-1 {
          animation: pulse-ring-1 2.5s cubic-bezier(0.25, 0.46, 0.45, 0.94) infinite;
        }
        
        .pulse-ring-2 {
          animation: pulse-ring-2 2.5s cubic-bezier(0.25, 0.46, 0.45, 0.94) infinite 0.8s;
        }
        
        .pulse-ring-3 {
          animation: pulse-ring-3 2.5s cubic-bezier(0.25, 0.46, 0.45, 0.94) infinite 1.6s;
        }
      `;
      document.head.appendChild(style);
    }
  }
};

// Fix for default markers in react-leaflet
delete L.Icon.Default.prototype._getIconUrl;
L.Icon.Default.mergeOptions({
  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',
  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',
  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',
});

// Custom marker icon
const customIcon = new L.Icon({
  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',
 
  iconAnchor: [16, 32],
  popupAnchor: [0, -32],
});

// Component to handle map clicks
function MapClickHandler({ onLocationSelect }) {
  useMapEvents({
    click: (e) => {
      onLocationSelect(e.latlng);
    },
    zoomend: () => {
      // Handle zoom end if needed
    },
    moveend: () => {
      // Handle move end if needed
    },
    wheel: (e) => {
      // Allow wheel events to pass through
      e.originalEvent.stopPropagation();
    },
  });
  return null;
}

// Google Maps Component
const GoogleMapComponent = ({ position, onLocationSelect, address }) => {
  const mapRef = useRef(null);
  const [mapLoaded, setMapLoaded] = useState(false);
  const [googleMap, setGoogleMap] = useState(null);
  const [marker, setMarker] = useState(null);
  const [pulsingOverlay, setPulsingOverlay] = useState(null);

  useEffect(() => {
    // Add pulse styles
    addPulseStyles();
    
    // Load Google Maps script only once
    const loadGoogleMaps = () => {
      if (window.google && window.google.maps) {
        setMapLoaded(true);
        return;
      }

      // Check if script is already loading
      if (document.querySelector('script[src*="maps.googleapis.com"]')) {
        return;
      }

      const script = document.createElement('script');
      script.src = `https://maps.googleapis.com/maps/api/js?key=${import.meta.env.VITE_GOOGLE_MAPS_API_KEY || 'AIzaSyB41DRUbKWJHPxaFjMAwdrzWzbVKartNGg'}&libraries=places,geometry`;
      script.async = true;
      script.defer = true;
      script.onload = () => setMapLoaded(true);
      document.head.appendChild(script);
    };

    loadGoogleMaps();
  }, []);

  useEffect(() => {
    if (!mapLoaded || !mapRef.current) return;

    // Only create map once
    if (googleMap) return;

    const coords = { lat: Number(position.lat), lng: Number(position.lng) };
    
    // Enhanced map styles for better clarity and professionalism
    const mapStyles = [
      {
        featureType: "poi",
        elementType: "labels",
        stylers: [{ visibility: "off" }]
      },
      {
        featureType: "transit",
        elementType: "labels",
        stylers: [{ visibility: "off" }]
      },
      {
        featureType: "landscape",
        elementType: "geometry",
        stylers: [{ color: "#f5f5f5" }]
      },
      {
        featureType: "road",
        elementType: "geometry",
        stylers: [{ color: "#ffffff" }]
      },
      {
        featureType: "road",
        elementType: "geometry.stroke",
        stylers: [{ color: "#e0e0e0" }]
      },
      {
        featureType: "water",
        elementType: "geometry",
        stylers: [{ color: "#e3f2fd" }]
      },
      {
        featureType: "administrative",
        elementType: "geometry.stroke",
        stylers: [{ color: "#c9c9c9" }]
      }
    ];
    
    const map = new window.google.maps.Map(mapRef.current, {
      center: coords,
      zoom: 16,
      mapTypeId: window.google.maps.MapTypeId.ROADMAP,
      mapTypeControl: false,
      streetViewControl: false,
      fullscreenControl: false,
      zoomControl: true,
      zoomControlOptions: {
        position: window.google.maps.ControlPosition.TOP_RIGHT,
        style: window.google.maps.ZoomControlStyle.SMALL
      },
      styles: mapStyles,
      backgroundColor: "#f8fafc",
      gestureHandling: 'cooperative' // Prevents accidental map movement
    });

    setGoogleMap(map);

    // Create professional circular marker with gradient and shadow
    const pulsingRedMarker = {
      url: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIiIGhlaWdodD0iMzIiIHZpZXdCb3g9IjAgMCAzMiAzMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMTYiIGN5PSIxNiIgcj0iMTYiIGZpbGw9InVybCgjZ3JhZGllbnQpIi8+CjxjaXJjbGUgY3g9IjE2IiBjeT0iMTYiIHI9IjEyIiBmaWxsPSJ3aGl0ZSIvPgo8Y2lyY2xlIGN4PSIxNiIgY3k9IjE2IiByPSI4IiBmaWxsPSJ1cmwoI2dyYWRpZW50SW5uZXIpIi8+CjxkZWZzPgo8bGluZWFyR3JhZGllbnQgaWQ9ImdyYWRpZW50IiB4MT0iMCIgeTE9IjAiIHgyPSIxIiB5Mj0iMSI+CjxzdG9wIG9mZnNldD0iMCUiIHN0eWxlPSJzdG9wLWNvbG9yOiNmNDQzMzY7c3RvcC1vcGFjaXR5OjEiIC8+CjxzdG9wIG9mZnNldD0iMTAwJSIgc3R5bGU9InN0b3AtY29sb3I6I2RjMjYyNjtzdG9wLW9wYWNpdHk6MSIgLz4KPC9saW5lYXJHcmFkaWVudD4KPGxpbmVhckdyYWRpZW50IGlkPSJncmFkaWVudElubmVyIiB4MT0iMCIgeTE9IjAiIHgyPSIxIiB5Mj0iMSI+CjxzdG9wIG9mZnNldD0iMCUiIHN0eWxlPSJzdG9wLWNvbG9yOiNmNDQzMzY7c3RvcC1vcGFjaXR5OjEiIC8+CjxzdG9wIG9mZnNldD0iMTAwJSIgc3R5bGU9InN0b3AtY29sb3I6I2VlMjIyMjtzdG9wLW9wYWNpdHk6MSIgLz4KPC9saW5lYXJHcmFkaWVudD4KPC9kZWZzPgo8L3N2Zz4K',
      scaledSize: new window.google.maps.Size(32, 32),
      anchor: new window.google.maps.Point(16, 16)
    };

    // Add marker with pulsing animation
    const newMarker = new window.google.maps.Marker({
      position: coords,
      map: map,
      title: 'Selected Location',
      icon: pulsingRedMarker,
      animation: window.google.maps.Animation.DROP,
      zIndex: 9999,
      optimized: false
    });

    setMarker(newMarker);

    // Create pulsing overlay that follows the marker
    class PulsingOverlay extends window.google.maps.OverlayView {
      constructor(position, map) {
        super();
        this.position = position;
        this.setMap(map);
      }

      onAdd() {
        this.div = document.createElement('div');
        this.div.style.position = 'absolute';
        this.div.style.pointerEvents = 'none';
        this.div.style.zIndex = '100';
        
        // Create pulsing elements - pulse rings first (back), then core (front)
        this.div.innerHTML = `
          <div class="pulse-ring-3" style="
            position: absolute;
            width: 32px;
            height: 32px;
            background: radial-gradient(circle, rgba(239, 68, 68, 0.3) 0%, transparent 60%);
            border-radius: 50%;
            transform: translate(-50%, -50%);
            box-shadow: 0 0 25px rgba(239, 68, 68, 0.2);
            z-index: 1;
          "></div>
          <div class="pulse-ring-2" style="
            position: absolute;
            width: 40px;
            height: 40px;
            background: radial-gradient(circle, rgba(239, 68, 68, 0.4) 0%, transparent 65%);
            border-radius: 50%;
            transform: translate(-50%, -50%);
            box-shadow: 0 0 20px rgba(239, 68, 68, 0.3);
            z-index: 2;
          "></div>
          <div class="pulse-ring-1" style="
            position: absolute;
            width: 48px;
            height: 48px;
            background: radial-gradient(circle, rgba(239, 68, 68, 0.5) 0%, transparent 70%);
            border-radius: 50%;
            transform: translate(-50%, -50%);
            box-shadow: 0 0 15px rgba(239, 68, 68, 0.4);
            z-index: 3;
          "></div>
          <div class="pulse-core" style="
            position: absolute;
            width: 32px;
            height: 32px;
            background: radial-gradient(circle, #ef4444 0%, #dc2626 100%);
            border-radius: 50%;
            transform: translate(-50%, -50%);
            box-shadow: 0 4px 16px rgba(239, 68, 68, 0.5), 0 0 0 3px rgba(255, 255, 255, 0.9);
            border: 2px solid rgba(255, 255, 255, 1);
            z-index: 10;
          "></div>
        `;
        
        const panes = this.getPanes();
        panes.overlayImage.appendChild(this.div);
      }

      draw() {
        if (!this.div) return;
        
        const projection = this.getProjection();
        if (!projection) return;
        
        const point = projection.fromLatLngToDivPixel(this.position);
        if (point) {
          this.div.style.left = point.x + 'px';
          this.div.style.top = point.y + 'px';
        }
      }

      // Method to update position
      updatePosition(newPosition) {
        this.position = newPosition;
        this.draw();
      }

      remove() {
        if (this.div && this.div.parentNode) {
          this.div.parentNode.removeChild(this.div);
        }
        this.setMap(null);
      }
    }

    // Add pulsing overlay
    const overlay = new PulsingOverlay(coords, map);
    setPulsingOverlay(overlay);

    // Update overlay position when map moves
    const updateOverlayPosition = () => {
      overlay.draw();
    };

    map.addListener('zoom_changed', updateOverlayPosition);
    map.addListener('center_changed', updateOverlayPosition);
    map.addListener('bounds_changed', updateOverlayPosition);

    // Add click listener to map
    map.addListener('click', (e) => {
      const newPosition = e.latLng;
      const coords = { lat: newPosition.lat(), lng: newPosition.lng() };
      
      // Update marker position without moving the map
      newMarker.setPosition(coords);
      
      // Update pulsing overlay position
      if (overlay && overlay.updatePosition) {
        overlay.updatePosition(coords);
      }
      
      // Call onLocationSelect without centering the map
      onLocationSelect(coords);
    });

  }, [mapLoaded, googleMap, onLocationSelect]);

  // Update marker position when position changes
  useEffect(() => {
    if (marker && position && googleMap) {
      const newPosition = { lat: Number(position.lat), lng: Number(position.lng) };
      marker.setPosition(newPosition);
      
      // Only center the map if it's the initial load or if the position is far from current center
      const currentCenter = googleMap.getCenter();
      
      // Calculate distance using Haversine formula as fallback
      const calculateDistance = (lat1, lon1, lat2, lon2) => {
        const R = 6371000; // Earth's radius in meters
        const dLat = (lat2 - lat1) * Math.PI / 180;
        const dLon = (lon2 - lon1) * Math.PI / 180;
        const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
                  Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
                  Math.sin(dLon/2) * Math.sin(dLon/2);
        const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
        return R * c;
      };
      
      let distance;
      if (window.google.maps.geometry && window.google.maps.geometry.spherical) {
        // Use Google Maps geometry library if available
        distance = window.google.maps.geometry.spherical.computeDistanceBetween(
          currentCenter,
          newPosition
        );
      } else {
        // Use Haversine formula as fallback
        distance = calculateDistance(
          currentCenter.lat(),
          currentCenter.lng(),
          newPosition.lat,
          newPosition.lng
        );
      }
      
      // Only center if distance is more than 1000 meters (1km)
      if (distance > 1000) {
        googleMap.setCenter(newPosition);
      }
      
      // Update pulsing overlay position using the new method
      if (pulsingOverlay && pulsingOverlay.updatePosition) {
        pulsingOverlay.updatePosition(newPosition);
      }
    }
  }, [position, marker, googleMap, pulsingOverlay]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (pulsingOverlay) {
        pulsingOverlay.remove();
      }
    };
  }, [pulsingOverlay]);

  if (!mapLoaded) {
    return (
      <div className="relative bg-gradient-to-br from-blue-50 to-indigo-100 rounded-lg border-2 border-dashed border-blue-300 flex items-center justify-center" style={{ height: '500px' }}>
        <div className="text-center">
          <div className="animate-spin rounded-full h-10 w-10 border-b-2 border-red-500 mx-auto mb-3"></div>
          <p className="text-sm text-blue-700 font-medium">Loading Google Maps...</p>
          <p className="text-xs text-blue-500 mt-1">Preparing interactive map</p>
        </div>
      </div>
    );
  }

  return (
    <div className="relative">
      <div 
        ref={mapRef} 
        className="w-full rounded-lg border-2 border-blue-200 overflow-hidden shadow-xl"
        style={{ 
          height: '500px',
          width: '100%',
          boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)'
        }}
      />
    </div>
  );
};

// Component to handle location search
function LocationSearch({ onLocationSelect, mapType = 'openstreetmap' }) {
  const [searchQuery, setSearchQuery] = useState('');
  const [isSearching, setIsSearching] = useState(false);
  const [searchResults, setSearchResults] = useState([]);
  const [searchError, setSearchError] = useState('');
  const searchTimeoutRef = useRef(null);

  const searchLocation = async (query) => {
    if (!query.trim()) {
      setSearchResults([]);
      setSearchError('');
      return;
    }

    setIsSearching(true);
    setSearchError('');

    try {
      let data;
      
      if (mapType === 'googlemaps' && window.google && window.google.maps) {
        // Use Google Places API
        const service = new window.google.maps.places.PlacesService(document.createElement('div'));
        const request = {
          query: query,
          fields: ['name', 'geometry', 'formatted_address']
        };
        
        data = await new Promise((resolve, reject) => {
          service.textSearch(request, (results, status) => {
            if (status === window.google.maps.places.PlacesServiceStatus.OK) {
              resolve(results.slice(0, 5).map(place => ({
                display_name: place.formatted_address,
                lat: place.geometry.location.lat().toString(),
                lon: place.geometry.location.lng().toString(),
                name: place.name
              })));
            } else {
              reject(new Error(`Google Places API error: ${status}`));
            }
          });
        });
      } else {
        // Use OpenStreetMap Nominatim
      const response = await fetch(
        `https://nominatim.openstreetmap.org/search?format=json&q=${encodeURIComponent(query)}&limit=5&addressdetails=1&countrycodes=jo,sa,ae,kw,qa,bh,om,iq,sy,lb,tr,eg,ps,ye,ma,tn,dz,us,uk,fr,de,ca,au,in,pk,bd,my,sg,ph,id,th`
      );
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
        data = await response.json();
      }
      
      if (data && Array.isArray(data)) {
        setSearchResults(data);
        if (data.length === 0) {
          setSearchError('No locations found. Try a different search term.');
        }
      } else {
        setSearchResults([]);
        setSearchError('Invalid response from search service.');
      }
    } catch (error) {
      console.error('Error searching location:', error);
      setSearchError('Failed to search location. Please try again.');
      setSearchResults([]);
    } finally {
      setIsSearching(false);
    }
  };

  // Debounced search
  const handleSearchInput = (e) => {
    const query = e.target.value;
    setSearchQuery(query);
    
    // Clear previous timeout
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }
    
    // Set new timeout for debounced search
    searchTimeoutRef.current = setTimeout(() => {
      if (query.trim().length >= 3) {
        searchLocation(query);
      } else {
        setSearchResults([]);
        setSearchError('');
      }
    }, 500);
  };

  const handleSearch = (e) => {
    e.preventDefault();
    if (searchQuery.trim().length >= 3) {
      searchLocation(searchQuery);
    }
  };

  const selectLocation = (result) => {
    const lat = parseFloat(result.lat);
    const lng = parseFloat(result.lon);
    
    if (isNaN(lat) || isNaN(lng)) {
      setSearchError('Invalid coordinates received from search result.');
      return;
    }
    
    onLocationSelect({ lat, lng }, result.display_name);
    setSearchQuery(result.display_name);
    setSearchResults([]);
    setSearchError('');
  };

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }
    };
  }, []);

  return (
    <div className="search-section">
      <form onSubmit={handleSearch} className="search-form">
        <div className="search-input-wrapper">
          <svg className="search-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
          </svg>
          <input
            type="text"
            value={searchQuery}
            onChange={handleSearchInput}
            placeholder="Search for company address..."
            className="search-input"
            minLength={3}
          />
        </div>
        <button
          type="submit"
          disabled={isSearching || searchQuery.trim().length < 3}
          className="search-btn"
        >
          {isSearching ? (
            <>
              <span className="loading-spinner"></span>
              Searching...
            </>
          ) : (
            <>
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
              Search
            </>
          )}
        </button>
      </form>
      
      {/* Error Message */}
      {searchError && (
        <div className="search-error">
          <svg className="error-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          {searchError}
        </div>
      )}
      
      {/* Enhanced Search Results */}
      {searchResults.length > 0 && (
        <div className="relative mt-4">
          <div className="absolute inset-0 bg-gradient-to-br from-blue-50 via-white to-indigo-50 rounded-2xl blur-xl opacity-60"></div>
          <div className="relative bg-white/80 backdrop-blur-xl rounded-2xl border border-white/40 shadow-2xl overflow-hidden">
            <div className="p-2">
          {searchResults.map((result, index) => (
            <div
              key={`${result.place_id || index}-${result.lat}-${result.lon}`}
              onClick={() => selectLocation(result)}
                  className="group relative p-4 rounded-xl transition-all duration-300 ease-out transform hover:scale-102 hover:bg-gradient-to-r hover:from-blue-50 hover:to-indigo-50 cursor-pointer border border-transparent hover:border-blue-200/50 hover:shadow-lg"
                  style={{
                    animationDelay: `${index * 100}ms`,
                    animation: 'slideInUp 0.5s ease-out forwards'
                  }}
                >
                  {/* Hover Background Effect */}
                  <div className="absolute inset-0 bg-gradient-to-r from-blue-500/5 to-indigo-500/5 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                  
                  {/* Content */}
                  <div className="relative flex items-start gap-4">
                    {/* Enhanced Icon */}
                    <div className="flex-shrink-0">
                      <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-lg flex items-center justify-center shadow-lg group-hover:shadow-xl transition-all duration-300 transform group-hover:scale-110">
                        <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2.5} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2.5} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                </svg>
              </div>
                      {/* Pulse Effect */}
                      <div className="absolute top-0 left-0 w-8 h-8 bg-blue-500/20 rounded-lg animate-ping"></div>
                    </div>
                    
                    {/* Enhanced Content */}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2 mb-2">
                        <h3 className="text-sm font-bold text-gray-900 group-hover:text-blue-900 transition-colors duration-300 truncate">
                          {result.display_name}
                        </h3>
                        {/* Status Badge */}
                        <div className="flex-shrink-0">
                          <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gradient-to-r from-emerald-100 to-teal-100 text-emerald-800 border border-emerald-200/50">
                            <div className="w-1.5 h-1.5 bg-emerald-500 rounded-full mr-1 animate-pulse"></div>
                            Verified
                          </span>
                        </div>
                      </div>
                      
                {result.address && (
                        <div className="flex items-center gap-2 text-xs text-gray-600 group-hover:text-gray-700 transition-colors duration-300">
                          <svg className="w-3 h-3 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                          </svg>
                          <span className="truncate">
                    {[
                      result.address.country,
                      result.address.state,
                      result.address.city,
                      result.address.town,
                      result.address.village
                    ].filter(Boolean).join(', ')}
                          </span>
                  </div>
                )}
                      
                      {/* Coordinates */}
                      <div className="flex items-center gap-2 mt-2 text-xs text-gray-500 group-hover:text-gray-600 transition-colors duration-300">
                        <svg className="w-3 h-3 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-1.447-.894L15 4m0 13V4m-6 3l6-3" />
                        </svg>
                        <span className="font-mono">
                          {Number(result.lat).toFixed(6)}, {Number(result.lon).toFixed(6)}
                        </span>
              </div>
                    </div>
                    
                    {/* Action Arrow */}
                    <div className="flex-shrink-0 flex items-center">
                      <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-lg flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-300 transform group-hover:scale-110 group-hover:translate-x-1">
                        <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2.5} d="M9 5l7 7-7 7" />
                        </svg>
                      </div>
                    </div>
                  </div>
                  
                  {/* Bottom Border Animation */}
                  <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-gradient-to-r from-blue-500 to-indigo-600 transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left"></div>
            </div>
          ))}
            </div>
          </div>
        </div>
      )}
      
      {/* Search Tips */}
      {searchQuery.trim().length > 0 && searchQuery.trim().length < 3 && (
        <div className="search-tips">
          <svg className="info-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          Type at least 3 characters to search
        </div>
      )}
    </div>
  );
}

const LocationPickerModal = ({ 
  isOpen, 
  onClose, 
  onLocationSelect, 
  initialLocation = null,
  fetchUserLocation = null,
  isSaving = false,
  userId = null,
  mode = 'edit' // New prop: 'select' for registration, 'edit' for settings
}) => {
  const [isUpdatingLocation, setIsUpdatingLocation] = useState(false);
  
  // Function to show professional error message
  const showErrorMessage = (errorMessage) => {
    // Create a custom error notification
    const notification = document.createElement('div');
    notification.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      background: linear-gradient(135deg, #f44336, #d32f2f);
      color: white;
      padding: 16px;
      border-radius: 8px;
      box-shadow: 0 4px 16px rgba(0,0,0,0.2);
      z-index: 10000;
      max-width: 320px;
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      font-size: 13px;
      line-height: 1.4;
      animation: slideInRight 0.3s ease-out;
      border-left: 3px solid #c62828;
    `;
    
    notification.innerHTML = `
      <div style="display: flex; align-items: flex-start; gap: 8px;">
        <div style="font-size: 20px; flex-shrink: 0;">❌</div>
        <div style="flex: 1;">
          <div style="font-weight: bold; font-size: 14px; margin-bottom: 4px;">Update Failed</div>
          <div style="font-size: 12px; opacity: 0.9; margin-bottom: 6px;">
            ${errorMessage || 'Unknown error occurred'}
          </div>
          <div style="font-size: 11px; opacity: 0.8;">
            Please try again
          </div>
        </div>
        <button onclick="this.parentElement.parentElement.remove()" style="
          background: none;
          border: none;
          color: white;
          font-size: 16px;
          cursor: pointer;
          padding: 0;
          margin-left: 4px;
          opacity: 0.7;
          transition: opacity 0.2s;
          line-height: 1;
        " onmouseover="this.style.opacity='1'" onmouseout="this.style.opacity='0.7'">×</button>
      </div>
    `;
    
    document.body.appendChild(notification);
    
    // Auto remove after 6 seconds (longer for errors)
    setTimeout(() => {
      if (notification.parentElement) {
        notification.style.animation = 'slideOutRight 0.3s ease-out';
        setTimeout(() => {
          if (notification.parentElement) {
            notification.remove();
          }
        }, 300);
      }
    }, 6000);
  };
  
  const [position, setPosition] = useState(initialLocation?.coordinates || { lat: 31.9539, lng: 35.9106 });
  const [address, setAddress] = useState(initialLocation?.address || '');
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingFromAPI, setIsLoadingFromAPI] = useState(false);
  const [selectedLocation, setSelectedLocation] = useState(null);
  const [locationError, setLocationError] = useState('');
  const [apiLocationLoaded, setApiLocationLoaded] = useState(false);
  const [hasInitialized, setHasInitialized] = useState(false);
  const [mapType, setMapType] = useState('googlemaps'); // 'openstreetmap' or 'googlemaps'
  const mapRef = useRef(null);

  // Function to update map view manually
  const updateMapView = (coords) => {
    if (mapRef.current && coords) {
      mapRef.current.setView([coords.lat, coords.lng], 15);
    }
  };

  // Fetch location from API (only in edit mode)
  const fetchLocationFromAPI = useCallback(async () => {
    if (!fetchUserLocation || mode !== 'edit') return;
    
    setIsLoadingFromAPI(true);
    setLocationError('');
    
    try {
      const locationData = await fetchUserLocation();
      
      if (locationData && locationData.latitude && locationData.longitude) {
        const newPosition = { 
          lat: parseFloat(locationData.latitude), 
          lng: parseFloat(locationData.longitude) 
        };
        setPosition(newPosition);
        setAddress(locationData.address || '');
        
        const newLocation = {
          coordinates: newPosition,
          address: locationData.address || '',
          formatted: locationData.address || ''
        };
        setSelectedLocation(newLocation);
        setApiLocationLoaded(true);
        setLocationError('');
        console.log('🌍 Location loaded from API:', newLocation);
        
        // Update map view for API location
        setTimeout(() => {
          updateMapView(newPosition);
        }, 500);
      } else {
        setLocationError('No location data found in your profile.');
      }
    } catch (error) {
      console.error('Error fetching location from API:', error);
      setLocationError('Failed to load location from profile. Please try again.');
    } finally {
      setIsLoadingFromAPI(false);
    }
  }, [fetchUserLocation, mode]);

  // Get current location
  const getCurrentLocation = () => {
    setIsLoading(true);
    setLocationError('');
    
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position) => {
          const { latitude, longitude } = position.coords;
          const newPosition = { lat: latitude, lng: longitude };
          setPosition(newPosition);
          reverseGeocode(newPosition);
          setIsLoading(false);
          
                  // Update map view for current location
        setTimeout(() => {
          updateMapView(newPosition);
        }, 100);
        },
        (error) => {
          console.error('Error getting location:', error);
          setIsLoading(false);
          switch(error.code) {
            case error.PERMISSION_DENIED:
              setLocationError('Location access denied. Please enable location services.');
              break;
            case error.POSITION_UNAVAILABLE:
              setLocationError('Location information unavailable.');
              break;
            case error.TIMEOUT:
              setLocationError('Location request timed out.');
              break;
            default:
              setLocationError('Failed to get current location.');
          }
        },
        {
          enableHighAccuracy: true,
          timeout: 10000,
          maximumAge: 60000
        }
      );
    } else {
      setIsLoading(false);
      setLocationError('Geolocation is not supported by this browser.');
    }
  };

  // Reverse geocoding to get address from coordinates
  const reverseGeocode = async (coords) => {
    try {
      const response = await fetch(
        `https://nominatim.openstreetmap.org/reverse?format=json&lat=${coords.lat}&lon=${coords.lng}&addressdetails=1&zoom=18`
      );
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const data = await response.json();
      
      if (data && data.display_name) {
        const fullAddress = data.display_name;
        setAddress(fullAddress);
        const newLocation = {
          coordinates: coords,
          address: fullAddress,
          formatted: fullAddress
        };
        setSelectedLocation(newLocation);
        setLocationError('');
        console.log('🌍 Location reverse geocoded:', newLocation);
        
        // Update map view for reverse geocoded location
        setTimeout(() => {
          updateMapView(coords);
        }, 100);
      } else {
        throw new Error('Invalid response from geocoding service');
      }
    } catch (error) {
      console.error('Error reverse geocoding:', error);
      setLocationError('Failed to get address for selected location.');
    }
  };

  // Handle location selection
  const handleLocationSelect = (coords, selectedAddress = null) => {
    setPosition(coords);
    setLocationError('');
    
    if (selectedAddress) {
      setAddress(selectedAddress);
      const newLocation = {
        coordinates: coords,
        address: selectedAddress,
        formatted: selectedAddress
      };
      setSelectedLocation(newLocation);
      console.log('🎯 Location selected from search:', newLocation);
      
              // Update map view for search results
        setTimeout(() => {
          updateMapView(coords);
        }, 100);
    } else {
      reverseGeocode(coords);
    }
  };



  // Initialize with initial location or fetch from API
  useEffect(() => {
    if (isOpen && !hasInitialized) {
      setHasInitialized(true);
      
      if (initialLocation && initialLocation.coordinates) {
        setPosition(initialLocation.coordinates);
        setAddress(initialLocation.address || '');
        setSelectedLocation(initialLocation);
        setLocationError('');
        setApiLocationLoaded(true);
        
        // Update map view for initial location
        setTimeout(() => {
          updateMapView(initialLocation.coordinates);
        }, 100);
      } else if (fetchUserLocation && mode === 'edit') {
        // Auto-fetch location from API when modal opens (only in edit mode)
        fetchLocationFromAPI();
      }
    }
  }, [isOpen, initialLocation, hasInitialized, fetchUserLocation, mode]);

  // Handle modal close
  const handleClose = () => {
    setLocationError('');
    setHasInitialized(false);
    onClose();
  };

  // Handle confirm location
  const handleConfirm = async () => {
    if (selectedLocation) {
      if (mode === 'select') {
        // In select mode, just return the location data without API call
        console.log('🗺️ Location selected for registration:', selectedLocation);
        onLocationSelect(selectedLocation);
        onClose();
      } else {
        // In edit mode, update the location via API
        if (isUpdatingLocation) return;
        
      console.log('🗺️ Modal Location Data to be sent in request:', {
        Address: selectedLocation.address,
        latitude: selectedLocation.coordinates?.lat,
        longitude: selectedLocation.coordinates?.lng,
        formatted: selectedLocation.formatted
      });

        // Get user ID - prioritize passed userId prop, then try localStorage
        let finalUserId = userId;
        let user = null;
        
        if (!finalUserId) {
          // Try different possible localStorage keys for user data
          const possibleUserKeys = ['user', 'currentUser', 'authUser', 'userData'];
          
          for (const key of possibleUserKeys) {
            try {
              const userData = localStorage.getItem(key);
              if (userData) {
                user = JSON.parse(userData);
                if (user && user.id) {
                  finalUserId = user.id;
                  console.log(`Found user ID in localStorage.${key}:`, finalUserId);
                  break;
                }
              }
            } catch (error) {
              console.warn(`Error parsing localStorage.${key}:`, error);
            }
          }
          
          // If no user ID found in localStorage, try sessionStorage
          if (!finalUserId) {
            for (const key of possibleUserKeys) {
              try {
                const userData = sessionStorage.getItem(key);
                if (userData) {
                  user = JSON.parse(userData);
                  if (user && user.id) {
                    finalUserId = user.id;
                    console.log(`Found user ID in sessionStorage.${key}:`, finalUserId);
                    break;
                  }
                }
              } catch (error) {
                console.warn(`Error parsing sessionStorage.${key}:`, error);
              }
            }
          }
          
          // If still no user ID, try direct localStorage keys
          if (!finalUserId) {
            const directKeys = ['user_id', 'userId', 'id'];
            for (const key of directKeys) {
              const directId = localStorage.getItem(key) || sessionStorage.getItem(key);
              if (directId) {
                finalUserId = directId;
                console.log(`Found user ID in direct key ${key}:`, finalUserId);
                break;
              }
            }
          }
          
          // If still no user ID, try to decode it from the token (if it's a JWT)
          if (!finalUserId) {
            const token = localStorage.getItem('token') || localStorage.getItem('authToken');
            if (token) {
              try {
                // Try to decode JWT token to get user ID
                const tokenParts = token.split('.');
                if (tokenParts.length === 3) {
                  const payload = JSON.parse(atob(tokenParts[1]));
                  if (payload && payload.user_id) {
                    finalUserId = payload.user_id;
                    console.log('Found user ID in JWT token:', finalUserId);
                  }
                }
              } catch (error) {
                console.warn('Error decoding JWT token:', error);
              }
            }
          }
        }
        
        if (!finalUserId) {
          console.error('User ID not found in any storage location');
          console.log('Available localStorage keys:', Object.keys(localStorage));
          console.log('Available sessionStorage keys:', Object.keys(sessionStorage));
          
          // Try to get user info from API if we have a token
          const tempToken = localStorage.getItem('token') || localStorage.getItem('authToken');
          if (tempToken) {
            try {
              console.log('Attempting to get user info from API...');
              const backendUrl = import.meta.env.VITE_BACKEND_URL || 'http://*************:8000/api';
              const userResponse = await fetch(`${backendUrl}/user`, {
                method: 'GET',
                headers: {
                  'Authorization': `Bearer ${tempToken}`,
                  'Accept': 'application/json'
                }
              });
              
              if (userResponse.ok) {
                const userData = await userResponse.json();
                if (userData && userData.id) {
                  finalUserId = userData.id;
                  console.log('Found user ID from API:', finalUserId);
                }
              }
            } catch (error) {
              console.warn('Error getting user info from API:', error);
            }
          }
          
          if (!finalUserId) {
            // Last resort: try to get user ID from the fetchUserLocation function
            if (fetchUserLocation) {
              try {
                console.log('Attempting to get user ID from fetchUserLocation...');
                const locationData = await fetchUserLocation();
                // If fetchUserLocation returns user data, extract ID
                if (locationData && locationData.user_id) {
                  finalUserId = locationData.user_id;
                  console.log('Found user ID from fetchUserLocation:', finalUserId);
                }
              } catch (error) {
                console.warn('Error getting user ID from fetchUserLocation:', error);
              }
            }
            
            if (!finalUserId) {
              alert('User ID not found. Please log in again or refresh the page.');
              return;
            }
          }
        }

        // Get auth token - try multiple possible keys
        let token = null;
        const possibleTokenKeys = ['token', 'authToken', 'apiToken', 'accessToken'];
        
        for (const key of possibleTokenKeys) {
          const storedToken = localStorage.getItem(key);
          if (storedToken) {
            token = storedToken;
            console.log(`Found token in localStorage.${key}`);
            break;
          }
        }
        
        // If no token in localStorage, try sessionStorage
        if (!token) {
          for (const key of possibleTokenKeys) {
            const storedToken = sessionStorage.getItem(key);
            if (storedToken) {
              token = storedToken;
              console.log(`Found token in sessionStorage.${key}`);
              break;
            }
          }
        }
        
        if (!token) {
          console.error('Authentication token not found in any storage location');
          console.log('Available localStorage keys:', Object.keys(localStorage));
          console.log('Available sessionStorage keys:', Object.keys(sessionStorage));
          alert('Authentication token not found. Please log in again or refresh the page.');
          return;
        }

        setIsUpdatingLocation(true);

        try {
          // Prepare location data for API
          const locationData = {
            latitude: selectedLocation.coordinates?.lat?.toString(),
            longitude: selectedLocation.coordinates?.lng?.toString(),
            address: selectedLocation.address
          };

          console.log('📡 Sending location update to API:', locationData);

          // Call the new location API
          const backendUrl = import.meta.env.VITE_BACKEND_URL || 'http://*************:8000/api';
          const response = await fetch(`${backendUrl}/datatable/users/${finalUserId}/location`, {
            method: 'PUT',
            headers: {
              'Content-Type': 'application/json',
              'Accept': 'application/json',
              'Authorization': `Bearer ${token}`
            },
            body: JSON.stringify(locationData)
          });

          const result = await response.json();

          if (response.ok) {
            console.log('✅ Location updated successfully:', result.data);
            
            // Call the original onLocationSelect with the updated data
      onLocationSelect(selectedLocation);
            
            // Close the modal
            onClose();
          } else {
            console.error('❌ Failed to update location:', result);
            
            // Show professional error message
            showErrorMessage(result.message || 'Unknown error occurred');
          }
        } catch (error) {
          console.error('❌ Error updating location:', error);
          showErrorMessage('Network error occurred. Please check your connection and try again.');
        } finally {
          setIsUpdatingLocation(false);
        }
      }
    }
  };

  // Handle escape key
  useEffect(() => {
    const handleEscape = (e) => {
      if (e.key === 'Escape') {
        handleClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscape);
      document.body.style.overflow = 'hidden';
    }

    return () => {
      document.removeEventListener('keydown', handleEscape);
      document.body.style.overflow = 'unset';
    };
  }, [isOpen]);

  if (!isOpen) return null;

  return (
    <div className="location-modal-overlay" onClick={handleClose}>
      <div className="location-modal" onClick={(e) => e.stopPropagation()}>
        {/* Modal Header */}
        <div className="modal-header">
          <div className="modal-title">
            <svg className="title-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
            </svg>
            <h2>{mode === 'select' ? 'Select Company Address' : 'Update Company Address'}</h2>
          </div>
          <button onClick={handleClose} className="close-btn">
            <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* Modal Content */}
        <div className="modal-content">
          {/* Loading Indicators - Only show in edit mode */}
          {mode === 'edit' && isLoadingFromAPI && (
            <div className="bg-gradient-to-r from-blue-500/20 to-purple-500/20 border border-blue-400/30 rounded-xl p-4 mb-6">
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 bg-blue-500/20 rounded-full flex items-center justify-center">
                  <span className="loading-spinner"></span>
                </div>
                <div>
                  <p className="text-blue-300 font-medium text-sm">Loading company address from profile...</p>
                  <p className="text-blue-400 text-xs">Please wait while we fetch your location data</p>
                </div>
              </div>
            </div>
          )}

          {mode === 'edit' && isSaving && (
            <div className="bg-gradient-to-r from-green-500/20 to-emerald-500/20 border border-green-400/30 rounded-xl p-4 mb-6">
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 bg-green-500/20 rounded-full flex items-center justify-center">
                  <span className="loading-spinner"></span>
                </div>
                <div>
                  <p className="text-green-300 font-medium text-sm">Saving company address...</p>
                  <p className="text-green-400 text-xs">Please wait while we update your location data</p>
                </div>
              </div>
            </div>
          )}

          {mode === 'edit' && isUpdatingLocation && (
            <div className="bg-gradient-to-r from-blue-500/20 to-indigo-500/20 border border-blue-400/30 rounded-xl p-4 mb-6">
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 bg-blue-500/20 rounded-full flex items-center justify-center">
                  <span className="loading-spinner"></span>
                </div>
                <div>
                  <p className="text-blue-300 font-medium text-sm">Updating company location...</p>
                  <p className="text-blue-400 text-xs">Please wait while we save your new location data</p>
                </div>
              </div>
            </div>
          )}

          {/* Map Type Toggle */}
          <div className="map-type-toggle mb-8">
            <div className="relative bg-gradient-to-br from-slate-100 via-blue-50 to-indigo-100 rounded-3xl p-4 shadow-2xl border border-white/30 backdrop-blur-xl pt-12 pb-6">
              {/* Advanced Background Patterns */}
              <div className="absolute inset-0 bg-gradient-to-br from-blue-500/8 via-transparent to-purple-600/8 rounded-3xl"></div>
              <div className="absolute inset-0 bg-[radial-gradient(circle_at_25%_25%,rgba(59,130,246,0.15),transparent_40%)] rounded-3xl"></div>
              <div className="absolute inset-0 bg-[radial-gradient(circle_at_75%_75%,rgba(147,51,234,0.1),transparent_40%)] rounded-3xl"></div>
              
              {/* Animated Background Elements */}
              <div className="absolute top-0 left-0 w-full h-full overflow-hidden rounded-3xl">
                <div className="absolute top-4 left-4 w-2 h-2 bg-gradient-to-r from-blue-400 to-purple-500 rounded-full opacity-60 animate-pulse"></div>
                <div className="absolute top-8 right-8 w-1.5 h-1.5 bg-gradient-to-r from-emerald-400 to-teal-500 rounded-full opacity-50 animate-pulse" style={{animationDelay: '0.5s'}}></div>
                <div className="absolute bottom-6 left-8 w-1 h-1 bg-gradient-to-r from-purple-400 to-pink-500 rounded-full opacity-40 animate-pulse" style={{animationDelay: '1s'}}></div>
              </div>
              
              {/* Toggle Container */}
              <div className="relative grid grid-cols-2 bg-white/70 backdrop-blur-xl rounded-2xl p-2 shadow-2xl border border-white/40 mb-6 overflow-hidden">
                {/* Animated Background Slider */}
                <div 
                  className={`absolute top-2 bottom-2 rounded-xl transition-all duration-500 ease-out shadow-2xl transform ${
                    mapType === 'googlemaps' 
                      ? 'left-2 w-[calc(50%-4px)] scale-100' 
                      : 'left-[calc(50%+2px)] w-[calc(50%-4px)] scale-100'
                  }`}
                  style={{
                    background: mapType === 'googlemaps' 
                      ? 'linear-gradient(135deg, #3b82f6 0%, #2563eb 50%, #1d4ed8 100%)' 
                      : 'linear-gradient(135deg, #10b981 0%, #059669 50%, #047857 100%)',
                    boxShadow: mapType === 'googlemaps'
                      ? '0 20px 40px -10px rgba(59, 130, 246, 0.6), 0 10px 20px -5px rgba(59, 130, 246, 0.4), inset 0 1px 0 rgba(255, 255, 255, 0.2)'
                      : '0 20px 40px -10px rgba(16, 185, 129, 0.6), 0 10px 20px -5px rgba(16, 185, 129, 0.4), inset 0 1px 0 rgba(255, 255, 255, 0.2)',
                    transform: mapType === 'googlemaps' 
                      ? 'translateX(0) scale(1.02)' 
                      : 'translateX(0) scale(1.02)'
                  }}
                ></div>
                
                {/* Google Maps Button */}
                <button
                  type="button"
                  onClick={() => setMapType('googlemaps')}
                  className={`relative z-10 w-full px-6 py-4 rounded-xl text-sm font-bold transition-all duration-400 ease-out transform ${
                    mapType === 'googlemaps'
                      ? 'text-white scale-105 shadow-2xl' 
                      : 'text-slate-700 hover:text-slate-900 hover:scale-102 hover:bg-white/40'
                  }`}
                  style={{
                    background: mapType === 'googlemaps' 
                      ? 'linear-gradient(135deg, #3b82f6 0%, #2563eb 50%, #1d4ed8 100%)' 
                      : 'transparent',
                    transition: 'all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94)',
                    textShadow: mapType === 'googlemaps' ? '0 1px 2px rgba(0,0,0,0.1)' : 'none'
                  }}
                >
                  <div className="flex items-center justify-center gap-3">
                    <div className={`relative transition-all duration-300 ${
                      mapType === 'googlemaps' ? 'scale-110' : 'scale-100'
                    }`}>
                      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2.5} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2.5} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                      </svg>
                      {mapType === 'googlemaps' && (
                        <div className="absolute -top-1 -right-1 w-2 h-2 bg-white rounded-full animate-pulse"></div>
                      )}
                    </div>
                    <span className="font-bold tracking-wide">Google Maps</span>
                    {mapType === 'googlemaps' && (
                      <div className="w-1.5 h-1.5 bg-white rounded-full animate-bounce"></div>
                    )}
                  </div>
                </button>
                
                {/* OpenStreetMap Button */}
                <button
                  type="button"
                  onClick={() => setMapType('openstreetmap')}
                  className={`relative z-10 w-full px-6 py-4 rounded-xl text-sm font-bold transition-all duration-400 ease-out transform ${
                    mapType === 'openstreetmap'
                      ? 'text-white scale-105 shadow-2xl' 
                      : 'text-slate-700 hover:text-slate-900 hover:scale-102 hover:bg-white/40'
                  }`}
                  style={{
                    background: mapType === 'openstreetmap' 
                      ? 'linear-gradient(135deg, #10b981 0%, #059669 50%, #047857 100%)' 
                      : 'transparent',
                    transition: 'all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94)',
                    textShadow: mapType === 'openstreetmap' ? '0 1px 2px rgba(0,0,0,0.1)' : 'none'
                  }}
                >
                  <div className="flex items-center justify-center gap-3">
                    <div className={`relative transition-all duration-300 ${
                      mapType === 'openstreetmap' ? 'scale-110' : 'scale-100'
                    }`}>
                      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2.5} d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-1.447-.894L15 4m0 13V4m-6 3l6-3" />
                      </svg>
                      {mapType === 'openstreetmap' && (
                        <div className="absolute -top-1 -right-1 w-2 h-2 bg-white rounded-full animate-pulse"></div>
                      )}
                    </div>
                    <span className="font-bold tracking-wide">OpenStreetMap</span>
                    {mapType === 'openstreetmap' && (
                      <div className="w-1.5 h-1.5 bg-white rounded-full animate-bounce"></div>
                    )}
                  </div>
                </button>
              </div>
              
              {/* Enhanced Decorative Elements */}
              <div className="absolute -top-3 -left-3 w-6 h-6 bg-gradient-to-br from-blue-400 via-purple-500 to-indigo-600 rounded-full opacity-70 animate-pulse shadow-lg"></div>
              <div className="absolute -bottom-3 -right-3 w-5 h-5 bg-gradient-to-br from-emerald-400 via-teal-500 to-cyan-600 rounded-full opacity-70 animate-pulse shadow-lg" style={{animationDelay: '0.8s'}}></div>
              <div className="absolute -top-2 right-4 w-3 h-3 bg-gradient-to-br from-purple-400 to-pink-500 rounded-full opacity-50 animate-pulse" style={{animationDelay: '1.2s'}}></div>
              
              {/* Professional Status Indicator */}
              <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                <div className="bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-700 text-white text-xs font-bold px-4 py-2 rounded-full shadow-2xl border border-white/30 backdrop-blur-sm">
                  <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-white rounded-full animate-ping shadow-sm"></div>
                    <span className="tracking-wide">Interactive Map Selector</span>
                    <div className="w-1 h-1 bg-white/60 rounded-full animate-pulse"></div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Search Section */}
          <LocationSearch onLocationSelect={handleLocationSelect} mapType={mapType} />

          {/* Current Location Button */}
          <button
            type="button"
            onClick={getCurrentLocation}
            disabled={isLoading}
            className="current-location-btn"
          >
            {isLoading ? (
              <>
                <span className="loading-spinner"></span>
                Getting location...
              </>
            ) : (
              <>
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                </svg>
                Use Current Location
              </>
            )}
          </button>

          {/* Location Error */}
          {locationError && (
            <div className="location-error">
              <svg className="error-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              {locationError}
            </div>
          )}

          {/* Map Container */}
          <div className="map-container" style={{ position: 'relative', zIndex: 1 }}>
            {mapType === 'googlemaps' ? (
              <GoogleMapComponent 
                position={position}
                onLocationSelect={handleLocationSelect}
                address={address}
              />
            ) : (
            <MapContainer
              ref={mapRef}
              center={[position.lat, position.lng]}
              zoom={15}
              className="map"
              style={{ height: '500px', width: '100%' }}
                scrollWheelZoom={true}
                doubleClickZoom={true}
                zoomControl={true}
                dragging={true}
                touchZoom={true}
                keyboard={true}
                wheelPxPerZoomLevel={60}
                wheelDebounceTime={40}
                wheelSensitivity={1}
            >
              <TileLayer
                attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
                url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
                  maxZoom={19}
                  minZoom={3}
              />
              <Marker position={[position.lat, position.lng]} icon={customIcon}>
                <Popup>
                  <div className="popup-content">
                    <strong>Selected Company Address</strong>
                    <br />
                    <small className="text-gray-600">{address}</small>
                  </div>
                </Popup>
              </Marker>
              <MapClickHandler onLocationSelect={handleLocationSelect} />
            </MapContainer>
            )}
          </div>

          {/* Address Display */}
          {address && (
            <div className="address-display">
              <div className="address-header">
                <svg className="address-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                </svg>
                <div className="address-label">
                  {mode === 'edit' && apiLocationLoaded ? 'Current Company Address:' : 'Selected Company Address:'}
                </div>
              </div>
              <div className="address-text">{address}</div>
              <div className="coordinates">
                Coordinates: {Number(position.lat).toFixed(6)}, {Number(position.lng).toFixed(6)}
              </div>
            </div>
          )}

          {/* Instructions */}
          <div className="instructions">
            <svg className="info-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            {mode === 'select' 
              ? 'Click on the map to select your company address or search for a location.'
              : 'Your company address is loaded automatically from your profile. Click on the map to update it or search for a new address.'
            }
          </div>
        </div>

        {/* Modal Footer */}
        <div className="modal-footer">
          <button onClick={handleClose} className="cancel-btn">
            Cancel
          </button>
          <button 
            onClick={handleConfirm} 
            className="confirm-btn"
            disabled={!selectedLocation || (mode === 'edit' && (isSaving || isUpdatingLocation))}
          >
            {mode === 'edit' && (isSaving || isUpdatingLocation) ? (
              <>
                <span className="loading-spinner"></span>
                Updating...
              </>
            ) : (
              mode === 'select' ? 'Select Address' : 'Update Company Address'
            )}
          </button>
        </div>
      </div>
    </div>
  );
};

export default LocationPickerModal;
